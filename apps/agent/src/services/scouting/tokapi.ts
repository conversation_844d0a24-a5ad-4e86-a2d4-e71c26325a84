import axios, { AxiosError } from 'axios';
import { env } from '@/lib/env';
import { TiktokChallengeSchema, TiktokVideoSchema } from '@repo/constants';
import { TiktokServiceSchema } from '@/schemas/tools_schema';

// Configuration for retry and timeout behavior
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  timeout: number;
  retryableStatusCodes: number[];
}

// Default configuration
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  timeout: 30000, // 30 seconds
  retryableStatusCodes: [408, 429, 500, 502, 503, 504],
};

// Custom error types
export class TokAPIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public isRetryable: boolean = false,
  ) {
    super(message);
    this.name = 'TokAPIError';
  }
}

export class TokAPIRateLimitError extends TokAPIError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, true);
    this.name = 'TokAPIRateLimitError';
  }
}

export class TokAPITimeoutError extends TokAPIError {
  constructor(message: string = 'Request timeout') {
    super(message, 408, true);
    this.name = 'TokAPITimeoutError';
  }
}

// Circuit breaker state
interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
}

// Rate limiter state
interface RateLimiterState {
  requests: number[];
  windowStart: number;
}

export interface TokAPIHashtagSearchResponse {
  ad_info: Record<string, any>;
  challenge_list: Array<{
    challenge_info: {
      cha_name: string;
      cid: string;
      desc: string;
      user_count: number;
      view_count: number;
      share_info?: {
        share_url?: string;
      };
    };
    items: any;
    position: any;
  }>;
  cursor: number;
  has_more: number;
  status_code: number;
}

export interface TokAPIVideoSearchResponse {
  search_item_list: Array<{
    aweme_info: {
      aweme_id: string;
      desc: string;
      create_time: number;
      author: {
        uid: string;
        nickname: string;
        unique_id: string;
        sec_uid: string;
        region: string;
        language: string;
        signature: string;
        follower_count: number;
        aweme_count: number;
        avatar_larger: {
          url_list: string[];
        };
        create_time: number;
        user_tags: string;
        youtube_channel_id: string;
        ins_id: string;
        twitter_id: string;
      };
      statistics?: {
        digg_count: number;
        comment_count: number;
        share_count: number;
        play_count?: number;
      };
      video: {
        play_addr: {
          url_list: string[];
        };
        cover: {
          url_list: string[];
        };
        duration: number;
      };
      group_id: string;
    };
  }>;
  cursor: number;
  has_more: number;
  status_code: number;
}

export interface TokAPIHashtagVideoSearchResponse {
  aweme_list: Array<{
    aweme_id: string;
    desc: string;
    create_time: number;
    author: {
      uid: string;
      nickname: string;
      unique_id: string;
      sec_uid: string;
      region: string;
      language: string;
      signature: string;
      follower_count: number;
      aweme_count: number;
      avatar_larger: {
        url_list: string[];
      };
      create_time: number;
      user_tags: string;
      youtube_channel_id: string;
      ins_id: string;
      twitter_id: string;
    };
    statistics?: {
      digg_count: number;
      comment_count: number;
      share_count: number;
      play_count?: number;
    };
    video: {
      play_addr: {
        url_list: string[];
      };
      cover: {
        url_list: string[];
      };
      duration: number;
    };
    group_id: string;
  }>;
  cursor: number;
  has_more: number;
  status_code: number;
}

export class TokAPIService implements TiktokServiceSchema {
  private baseUrl = 'https://tokapi-mobile-version.p.rapidapi.com';
  private apiKey: string;
  private apiHost = 'tokapi-mobile-version.p.rapidapi.com';
  private retryConfig: RetryConfig;
  private circuitBreaker: CircuitBreakerState;
  private rateLimiter: RateLimiterState;

  // Circuit breaker configuration
  private readonly CIRCUIT_BREAKER_THRESHOLD = 3;
  private readonly CIRCUIT_BREAKER_TIMEOUT = 30000; // 30 seconds

  // Rate limiter configuration (requests per minute)
  private readonly RATE_LIMIT = 150;
  private readonly RATE_WINDOW = 60000; // 1 minute

  constructor(retryConfig: Partial<RetryConfig> = {}) {
    this.apiKey = env.RAPIDAPI_KEY;
    this.retryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };

    // Initialize circuit breaker
    this.circuitBreaker = {
      failures: 0,
      lastFailureTime: 0,
      state: 'CLOSED',
    };

    // Initialize rate limiter
    this.rateLimiter = {
      requests: [],
      windowStart: Date.now(),
    };
  }

  private getHeaders() {
    return {
      'x-rapidapi-host': this.apiHost,
      'x-rapidapi-key': this.apiKey,
    };
  }

  /**
   * Check if circuit breaker allows requests
   */
  private isCircuitBreakerOpen(): boolean {
    const now = Date.now();

    if (this.circuitBreaker.state === 'OPEN') {
      if (
        now - this.circuitBreaker.lastFailureTime >
        this.CIRCUIT_BREAKER_TIMEOUT
      ) {
        this.circuitBreaker.state = 'HALF_OPEN';
        console.log('Circuit breaker moved to HALF_OPEN state');
        return false;
      }
      return true;
    }

    return false;
  }

  /**
   * Record circuit breaker success
   */
  private recordCircuitBreakerSuccess(): void {
    this.circuitBreaker.failures = 0;
    if (this.circuitBreaker.state === 'HALF_OPEN') {
      this.circuitBreaker.state = 'CLOSED';
      console.log('Circuit breaker moved to CLOSED state');
    }
  }

  /**
   * Record circuit breaker failure
   */
  private recordCircuitBreakerFailure(): void {
    this.circuitBreaker.failures++;
    this.circuitBreaker.lastFailureTime = Date.now();

    if (this.circuitBreaker.failures >= this.CIRCUIT_BREAKER_THRESHOLD) {
      this.circuitBreaker.state = 'OPEN';
      console.warn(
        `Circuit breaker OPENED after ${this.circuitBreaker.failures} failures`,
      );
    }
  }

  /**
   * Check rate limit
   */
  private checkRateLimit(): boolean {
    const now = Date.now();

    // Clean old requests outside the window
    this.rateLimiter.requests = this.rateLimiter.requests.filter(
      (timestamp) => now - timestamp < this.RATE_WINDOW,
    );

    if (this.rateLimiter.requests.length >= this.RATE_LIMIT) {
      return false;
    }

    this.rateLimiter.requests.push(now);
    return true;
  }

  /**
   * Sleep for a given number of milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Calculate exponential backoff delay
   */
  private calculateDelay(attempt: number): number {
    const delay = this.retryConfig.baseDelay * Math.pow(2, attempt);
    const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
    return Math.min(delay + jitter, this.retryConfig.maxDelay);
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (error instanceof TokAPIError) {
      return error.isRetryable;
    }

    if (axios.isAxiosError(error)) {
      // Network errors are retryable
      if (!error.response) {
        return true;
      }

      // Check status codes
      const status = error.response.status;
      return this.retryConfig.retryableStatusCodes.includes(status);
    }

    // Timeout errors are retryable
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return true;
    }

    return false;
  }

  /**
   * Create error from axios error
   */
  private createErrorFromAxiosError(error: AxiosError): TokAPIError {
    if (!error.response) {
      return new TokAPITimeoutError('Network error or timeout');
    }

    const status = error.response.status;
    const responseData = error.response.data as any;
    const message =
      responseData?.message || responseData?.error || error.message;

    switch (status) {
      case 429:
        return new TokAPIRateLimitError(message);
      case 408:
        return new TokAPITimeoutError(message);
      default:
        return new TokAPIError(
          message,
          status,
          this.retryConfig.retryableStatusCodes.includes(status),
        );
    }
  }

  /**
   * Execute HTTP request with retry logic, circuit breaker, and rate limiting
   */
  private async executeWithRetry<T>(
    operation: string,
    requestFn: () => Promise<T>,
  ): Promise<T> {
    // Check circuit breaker
    if (this.isCircuitBreakerOpen()) {
      throw new TokAPIError('Circuit breaker is open', 503, false);
    }

    // Check rate limit
    if (!this.checkRateLimit()) {
      throw new TokAPIRateLimitError('Rate limit exceeded');
    }

    let lastError: Error = new TokAPIError('Unknown error');
    const startTime = Date.now();

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        console.log(
          `[TokAPI] ${operation} - Attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1}`,
        );

        const result = await requestFn();

        // Record success for circuit breaker
        this.recordCircuitBreakerSuccess();

        const duration = Date.now() - startTime;
        console.log(`[TokAPI] ${operation} - Success in ${duration}ms`);

        return result;
      } catch (error) {
        lastError = error as Error;

        // Convert axios errors to our custom errors
        if (axios.isAxiosError(error)) {
          lastError = this.createErrorFromAxiosError(error);
        }

        const duration = Date.now() - startTime;
        console.warn(
          `[TokAPI] ${operation} - Attempt ${attempt + 1} failed after ${duration}ms:`,
          lastError.message,
        );

        // Record failure for circuit breaker
        this.recordCircuitBreakerFailure();

        // Don't retry if error is not retryable or we've exhausted retries
        if (
          !this.isRetryableError(lastError) ||
          attempt === this.retryConfig.maxRetries
        ) {
          break;
        }

        // Calculate delay and wait before retry
        const delay = this.calculateDelay(attempt);
        console.log(`[TokAPI] ${operation} - Retrying in ${delay}ms...`);
        await this.sleep(delay);
      }
    }

    const totalDuration = Date.now() - startTime;
    console.error(
      `[TokAPI] ${operation} - Failed after ${this.retryConfig.maxRetries + 1} attempts in ${totalDuration}ms`,
    );
    throw lastError;
  }

  /**
   * Search for videos on TikTok (direct)
   * @param keyword The hashtag to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   * @param sort_type Sort type (0: relevance, 1: most liked)
   * @param publish_time Publish time (0: unlimited, 1: last 24 hours, 7: past week, 30: past month, 90: past 3 months, 180: past 6 months)
   */
  async searchVideos(
    keyword: string,
    offset = 0,
    count = 20,
    sort_type = 0,
    publish_time = 0,
  ): Promise<TiktokVideoSchema[]> {
    const operation = `Search videos for "${keyword}"`;

    const response = await this.executeWithRetry(operation, async () => {
      return await axios.get<TokAPIVideoSearchResponse>(
        `${this.baseUrl}/v1/search/post`,
        {
          headers: this.getHeaders(),
          params: { keyword, offset, count, sort_type, publish_time },
          timeout: this.retryConfig.timeout,
        },
      );
    });

    return response.data.search_item_list.map((video) => ({
      title: video.aweme_info.desc,
      hashtags: this.extractHashtags(video.aweme_info.desc),
      description: video.aweme_info.desc,
      platform: 'tiktok',
      video_id: video.aweme_info.aweme_id,
      video_url: video.aweme_info.video?.play_addr?.url_list?.[0] || '',
      thumbnail_url: video.aweme_info.video?.cover?.url_list?.[0] || '',
      publish_time: new Date(video.aweme_info.create_time * 1000).toISOString(),
      duration: video.aweme_info.video?.duration || 0,
      view_count: video.aweme_info.statistics?.play_count || 0,
      like_count: video.aweme_info.statistics?.digg_count || 0,
      comment_count: video.aweme_info.statistics?.comment_count || 0,
      share_count: video.aweme_info.statistics?.share_count || 0,
      author: {
        uid: video.aweme_info.author?.uid,
        nickname: video.aweme_info.author?.nickname,
        unique_id: video.aweme_info.author?.unique_id,
        sec_uid: video.aweme_info.author?.sec_uid,
        region: video.aweme_info.author?.region,
        language: video.aweme_info.author?.language,
        signature: video.aweme_info.author?.signature,
        aweme_count: video.aweme_info.author?.aweme_count,
        follower_count: video.aweme_info.author?.follower_count,
        avatar_url: video.aweme_info.author?.avatar_larger?.url_list?.[0] || '',
        create_time: video.aweme_info.author?.create_time,
        user_tags: video.aweme_info.author?.user_tags,
        youtube_channel_id: video.aweme_info.author?.youtube_channel_id,
        ins_id: video.aweme_info.author?.ins_id,
        twitter_id: video.aweme_info.author?.twitter_id,
      },
    }));
  }

  /**
   * Search for hashtags on TikTok
   * @param keyword The hashtag to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async searchHashtag(
    keyword: string,
    offset = 0,
    count = 10,
  ): Promise<TiktokChallengeSchema[]> {
    const operation = `Search hashtag for "${keyword}"`;

    const response = await this.executeWithRetry(operation, async () => {
      return await axios.get<TokAPIHashtagSearchResponse>(
        `${this.baseUrl}/v1/search/hashtag`,
        {
          headers: this.getHeaders(),
          params: { keyword, offset, count },
          timeout: this.retryConfig.timeout,
        },
      );
    });

    return response.data.challenge_list.map((challenge) => ({
      challenge_id: challenge.challenge_info.cid,
      challenge_name: challenge.challenge_info.cha_name,
      use_count: 0,
      user_count: challenge.challenge_info.user_count,
      view_count: challenge.challenge_info.view_count,
    }));
  }

  /**
   * Get videos for a specific creator
   * @param user_id The creator's user ID
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async getCreatorPosts(
    user_id: string,
    offset = 0,
    count = 10,
  ): Promise<TiktokVideoSchema[]> {
    const operation = `Get creator posts for user_id "${user_id}"`;

    const response = await this.executeWithRetry(operation, async () => {
      return await axios.get<TokAPIHashtagVideoSearchResponse>(
        `${this.baseUrl}/v1/post/user/posts`,
        {
          headers: this.getHeaders(),
          params: { user_id, count, offset },
          timeout: this.retryConfig.timeout,
        },
      );
    });

    return response.data.aweme_list.map((video) => ({
      title: video.desc,
      hashtags: this.extractHashtags(video.desc),
      description: video.desc,
      platform: 'tiktok',
      video_id: video.aweme_id,
      video_url: video.video?.play_addr?.url_list?.[0] || '',
      thumbnail_url: video.video?.cover?.url_list?.[0] || '',
      publish_time: new Date(video.create_time * 1000).toISOString(),
      duration: video.video?.duration || 0,
      view_count: video.statistics?.play_count || 0,
      like_count: video.statistics?.digg_count || 0,
      comment_count: video.statistics?.comment_count || 0,
      share_count: video.statistics?.share_count || 0,
      author: {
        uid: video.author?.uid,
        nickname: video.author?.nickname,
        unique_id: video.author?.unique_id,
        sec_uid: video.author?.sec_uid,
        region: video.author?.region,
        language: video.author?.language,
        signature: video.author?.signature,
        aweme_count: video.author?.aweme_count,
        follower_count: video.author?.follower_count,
        avatar_url: video.author?.avatar_larger?.url_list?.[0] || '',
        create_time: video.author?.create_time,
        user_tags: video.author?.user_tags,
        youtube_channel_id: video.author?.youtube_channel_id,
        ins_id: video.author?.ins_id,
        twitter_id: video.author?.twitter_id,
      },
    }));
  }

  /**
   * Get videos for a specific hashtag
   * @param challengeId The challenge/hashtag ID
   * @param cursor Pagination cursor
   * @param count Number of results to return
   */
  async getHashtagVideos(
    challengeId: string,
    cursor = 0,
    count = 10,
  ): Promise<TiktokVideoSchema[]> {
    // Maximum items per page is 25
    const MAX_ITEMS_PER_PAGE = 25;
    let allVideos: TiktokVideoSchema[] = [];
    let currentCursor = cursor;
    let remainingCount = count;

    // Fetch pages until we have enough videos or there are no more results
    while (remainingCount > 0) {
      // Calculate how many items to fetch in this request
      const itemsToFetch = Math.min(remainingCount, MAX_ITEMS_PER_PAGE);
      const operation = `Get hashtag videos for challenge "${challengeId}" (cursor: ${currentCursor}, count: ${itemsToFetch})`;

      const response = await this.executeWithRetry(operation, async () => {
        return await axios.get<TokAPIHashtagVideoSearchResponse>(
          `${this.baseUrl}/v1/hashtag/posts/${challengeId}`,
          {
            headers: this.getHeaders(),
            params: { count: itemsToFetch, offset: currentCursor },
            timeout: this.retryConfig.timeout,
          },
        );
      });

      const videos = response.data.aweme_list.map((video) => ({
        title: video.desc,
        hashtags: this.extractHashtags(video.desc),
        description: video.desc,
        platform: 'tiktok',
        video_id: video.aweme_id,
        video_url: video.video?.play_addr?.url_list?.[0] || '',
        thumbnail_url: video.video?.cover?.url_list?.[0] || '',
        publish_time: new Date(video.create_time * 1000).toISOString(),
        duration: video.video?.duration || 0,
        view_count: video.statistics?.play_count || 0,
        like_count: video.statistics?.digg_count || 0,
        comment_count: video.statistics?.comment_count || 0,
        share_count: video.statistics?.share_count || 0,
        author: {
          uid: video.author?.uid,
          nickname: video.author?.nickname,
          unique_id: video.author?.unique_id,
          sec_uid: video.author?.sec_uid,
          region: video.author?.region,
          language: video.author?.language,
          signature: video.author?.signature,
          aweme_count: video.author?.aweme_count,
          follower_count: video.author?.follower_count,
          avatar_url: video.author?.avatar_larger?.url_list?.[0] || '',
          create_time: video.author?.create_time,
          user_tags: video.author?.user_tags,
          youtube_channel_id: video.author?.youtube_channel_id,
          ins_id: video.author?.ins_id,
          twitter_id: video.author?.twitter_id,
        },
      }));

      allVideos = [...allVideos, ...videos];

      // Update remaining count
      remainingCount -= videos.length;

      // If we didn't get as many videos as requested or there are no more results, break
      if (videos.length < itemsToFetch || !response.data.has_more) {
        break;
      }

      // Update cursor for next page
      currentCursor = response.data.cursor;
    }

    return allVideos;
  }

  /**
   * Extract hashtags from a string
   * @param text Text containing hashtags
   */
  private extractHashtags(text: string): string[] {
    const hashtagRegex = /#(\w+)/g;
    const matches = text.match(hashtagRegex);
    return matches ? matches.map((tag) => tag.substring(1)) : [];
  }

  /**
   * Get current service health status
   */
  getHealthStatus() {
    return {
      serviceName: 'TokAPI',
      circuitBreaker: {
        state: this.circuitBreaker.state,
        failures: this.circuitBreaker.failures,
        lastFailureTime: this.circuitBreaker.lastFailureTime,
      },
      rateLimiter: {
        requestsInWindow: this.rateLimiter.requests.length,
        limit: this.RATE_LIMIT,
        windowStart: this.rateLimiter.windowStart,
      },
      config: {
        maxRetries: this.retryConfig.maxRetries,
        baseDelay: this.retryConfig.baseDelay,
        maxDelay: this.retryConfig.maxDelay,
        timeout: this.retryConfig.timeout,
      },
    };
  }

  /**
   * Reset circuit breaker (for manual recovery)
   */
  resetCircuitBreaker(): void {
    this.circuitBreaker = {
      failures: 0,
      lastFailureTime: 0,
      state: 'CLOSED',
    };
    console.log('[TokAPI] Circuit breaker manually reset');
  }

  /**
   * Update retry configuration
   */
  updateRetryConfig(newConfig: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...newConfig };
    console.log('[TokAPI] Retry configuration updated:', this.retryConfig);
  }

  /**
   * Perform a health check by making a simple API call
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Make a simple search request to test connectivity
      await this.searchHashtag('test', 0, 1);
      return true;
    } catch (error) {
      console.error('[TokAPI] Health check failed:', error);
      return false;
    }
  }
}
