import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { join } from 'path';

/**
 * Comprehensive creator scouting results structure for saving to file
 */
export interface SavedCreatorResults {
  metadata: {
    timestamp: string;
    inputParameters: any;
    summary: {
      totalScoutedCreators: number;
      totalQualifiedCreators: number;
      targetCreatorCount: number;
      filterMode: string;
      useIntelligentSelection: boolean;
    };
    filterSummary?: {
      mode: string;
      total_analyzed: number;
      total_qualified: number;
      avg_match_score: number;
      tier_breakdown: {
        PERFECT: number;
        EXCELLENT: number;
        GOOD: number;
        ACCEPTABLE: number;
      };
    };
  };

  // Detailed format - full creator data
  detailed: {
    creators: Array<{
      url: string;
      reason: string;
      match_score?: number;
      tier?: 'PERFECT' | 'EXCELLENT' | 'GOOD' | 'ACCEPTABLE';
      creatorMetrics?: {
        unique_id: string;
        nickname: string;
        follower_count?: number;
        aweme_count?: number;
        create_time?: number;
        region?: string;
        language?: string;
        recentVideosCollected: number;
        medianViews: number;
        medianLikes: number;
        medianComments: number;
        medianShares: number;
        avgEngagementRate: number;
        totalViews: number;
        totalLikes: number;
        totalComments: number;
        totalShares: number;
      };
    }>;
    totalCount: number;
  };

  // Summary format - essential information only
  summary: {
    totalQualified: number;
    targetCount: number;
    filterMode: string;
    topCreators: Array<{
      handle: string;
      url: string;
      score?: number;
      tier?: string;
      followers?: number;
      engagement?: number;
      reason: string;
    }>;
  };

  // CSV-ready format - flattened data for spreadsheet export
  csvReady: Array<{
    handle: string;
    nickname: string;
    url: string;
    match_score: number;
    tier: string;
    follower_count: number;
    post_count: number;
    median_views: number;
    median_likes: number;
    engagement_rate: number;
    region: string;
    language: string;
    reason: string;
  }>;
}

/**
 * Save creator scouting results to a comprehensive JSON file with multiple formats
 * @param results The workflow results to save
 * @param inputData The original input parameters
 * @param customDir Optional custom directory to save results (default: 'scouting-results')
 * @param customFilename Optional custom filename prefix (default: 'creator-scout-results')
 * @returns The full path to the saved file
 */
export function saveCreatorResultsToFile(
  executeResult: any,
  inputData: any,
  customDir?: string,
  customFilename?: string,
): string {
  try {
    // Create results directory if it doesn't exist
    const resultsDir = join(process.cwd(), customDir || 'scouting-results');
    mkdirSync(resultsDir, { recursive: true });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filenamePrefix = customFilename || 'creator-scout-results';
    const filename = `${filenamePrefix}-${timestamp}.json`;
    const filepath = join(resultsDir, filename);

    const results = executeResult.result;

    // Extract creator data from results.results (this is where the actual data is)
    const creatorResults = results.results || [];

    // Prepare comprehensive results data with multiple formats
    const comprehensiveResults = {
      metadata: {
        timestamp: new Date().toISOString(),
        inputParameters: inputData,
        summary: {
          totalScoutedCreators: results.scoutedCreators || 0,
          totalQualifiedCreators: results.qualifiedCreators || 0,
          targetCreatorCount: inputData.desiredCreatorCount || 0,
          filterMode: inputData.filterMode || 'UNKNOWN',
          useIntelligentSelection:
            inputData.useIntelligentChallengeSelection || false,
        },
        filterSummary: results.filterSummary,
      },

      // Detailed format - full creator data
      detailed: {
        creators: creatorResults,
        totalCount: creatorResults.length,
      },

      // Summary format - essential information only
      summary: {
        totalQualified: results.qualifiedCreators || 0,
        targetCount: inputData.desiredCreatorCount || 0,
        filterMode: inputData.filterMode || 'UNKNOWN',
        topCreators: creatorResults.slice(0, 10).map((creator: any) => ({
          handle: creator.creatorMetrics?.unique_id || 'N/A',
          url: creator.url,
          score: creator.match_score,
          tier: creator.tier,
          followers: creator.creatorMetrics?.follower_count,
          engagement: creator.creatorMetrics?.avgEngagementRate,
          reason: creator.reason,
        })),
      },

      // CSV-ready format - flattened data for spreadsheet export
      csvReady: creatorResults.map((creator: any) => ({
        handle: creator.creatorMetrics?.unique_id || '',
        nickname: creator.creatorMetrics?.nickname || '',
        url: creator.url,
        match_score: creator.match_score || 0,
        tier: creator.tier || '',
        follower_count: creator.creatorMetrics?.follower_count || 0,
        post_count: creator.creatorMetrics?.aweme_count || 0,
        median_views: creator.creatorMetrics?.medianViews || 0,
        median_likes: creator.creatorMetrics?.medianLikes || 0,
        engagement_rate: creator.creatorMetrics?.avgEngagementRate || 0,
        region: creator.creatorMetrics?.region || '',
        language: creator.creatorMetrics?.language || '',
        reason: creator.reason || '',
      })),
    };

    // Save to file with pretty formatting
    writeFileSync(
      filepath,
      JSON.stringify(comprehensiveResults, null, 2),
      'utf8',
    );

    // Log success information
    console.log(`\n💾 COMPREHENSIVE RESULTS SAVED TO FILE:`);
    console.log(`📁 Location: ${filepath}`);
    console.log(`📊 Total Creators: ${creatorResults.length}`);
    console.log(`📋 Formats included: Detailed, Summary, CSV-ready`);

    if (results.filterSummary?.tier_breakdown) {
      console.log(
        `🏆 PERFECT Tier: ${results.filterSummary.tier_breakdown.PERFECT || 0}`,
      );
      console.log(
        `⭐ EXCELLENT Tier: ${results.filterSummary.tier_breakdown.EXCELLENT || 0}`,
      );
      console.log(
        `👍 GOOD Tier: ${results.filterSummary.tier_breakdown.GOOD || 0}`,
      );
      console.log(
        `✅ ACCEPTABLE Tier: ${results.filterSummary.tier_breakdown.ACCEPTABLE || 0}`,
      );
    }

    return filepath;
  } catch (error) {
    console.error('❌ Error saving results to file:', error);
    throw error;
  }
}

/**
 * Save results with a custom format for analysis or reporting
 * @param results The workflow results
 * @param inputData The input parameters
 * @param format The output format ('detailed' | 'summary' | 'csv-ready')
 * @returns The path to the saved file
 */
export function saveCreatorResultsWithFormat(
  results: any,
  inputData: any,
  _format: 'detailed' | 'summary' | 'csv-ready' = 'detailed',
): string {
  console.log(
    `⚠️  saveCreatorResultsWithFormat is deprecated. All formats are now included in one comprehensive file.`,
  );
  return saveCreatorResultsToFile(results, inputData);
}

/**
 * Check if results directory exists and create it if needed
 * @param customDir Optional custom directory name
 */
export function ensureResultsDirectory(customDir?: string): string {
  const resultsDir = join(process.cwd(), customDir || 'scouting-results');

  if (!existsSync(resultsDir)) {
    mkdirSync(resultsDir, { recursive: true });
    console.log(`📁 Created results directory: ${resultsDir}`);
  }

  return resultsDir;
}
